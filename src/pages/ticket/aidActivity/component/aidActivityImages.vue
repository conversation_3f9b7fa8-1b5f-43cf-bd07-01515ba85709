<template>
    <div class="content-activity-image">
        <div
            class="content-activity-image-item"
            v-for="(item, index) in list"
            :key="index"
        >
            <div
                v-if="item.ifLongImage"
                class="content-activity-long-image-item"
            >
                <img
                    v-for="(img, index) in item.longIamges"
                    class="activity-img"
                    :src="img.image_url"
                    :key="index"
                />
            </div>
            <img v-else class="activity-img" :src="item.image_url" />
            <div v-if="item.image_desc" class="desc">
                <span class="dot"></span>
                {{ item.image_desc }}
            </div>
        </div>
    </div>
</template>

<script>
import { processImagesWithGroupId } from '../utils/imageUtils';

export default {
    name: 'AidActivityImages',
    props: {
        images: {
            type: Array,
            required: true,
            default: () => [],
        },
    },
    computed: {
        list() {
            return processImagesWithGroupId(this.images);
        },
    },
};
</script>

<style lang="scss">
.content-activity-image {
    .content-activity-image-item {
        margin: 30px 0;

        .content-activity-long-image-item {
            font-size: 0;
            img {
                margin: 0;
                height: auto;
            }
        }

        .activity-img {
            width: 760px;
        }

        .desc {
            margin-top: 10px;
            display: flex;

            .dot {
                margin: 8px 10px 0 0;
                display: inline-block;
                width: 4px;
                height: 4px;
                border-radius: 50%;
                background: #999999;
            }
        }
    }
}

.content-activity-map {
    margin: 20px 0 40px;

    .image {
        cursor: pointer;
        width: 100%;
    }
}
</style>
