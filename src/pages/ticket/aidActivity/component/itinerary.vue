<template>
    <div v-if="itineraryInfo">
        <div class="itinerary">
            <klk-section-title size="h2" decorative-line>
                {{ $t('itinerary') }}
            </klk-section-title>
        </div>
        <KlkExperienceItinerary
            :klook="klook || {}"
            :language="language"
            :hack-close-btn="hackCloseBtn"
            :itinerary-data="itineraryData"
            :get-translate-func="getTranslateFunc"
            :get-axios-func="getAxiosFunc"
            :platform="platform"
            :map-type="mapType"
            :simplified="simplified"
            :inhouse-track="inhouseTrack"
            :custom-flow-type="customFlowProps.customFlowType"
            :custom-flow-data="customFlowProps.customFlowData"
        />
    </div>
</template>

<script>
import KlkExperienceItinerary from '@klook/klook-experience-itinerary-v2';
import Vue from 'vue';
import '@klook/klook-experience-itinerary-v2/dist/esm/index.css';
import 'swiper/css/swiper.css';
import { GalileoVuePlugin } from '@klook/galileo-vue';
import lazyloadPlugin from '@/plugins/lazyload';

Vue.use(GalileoVuePlugin);
lazyloadPlugin();

export default {
    name: 'KlookExperienceItinerary',
    components: {
        KlkExperienceItinerary,
    },
    props: {
        simplified: {
            type: Boolean,
            default: false,
        },
        hackCloseBtn: {
            type: Boolean,
            default: false,
        },
        // grounpTitleClick: {
        //     type: Function,
        //     default: () => {},
        // },
        customFlowProps: {
            type: Object,
            default: () => ({}),
        },
        currency: {
            type: String,
            required: true,
        },
        spuId: {
            type: [String, Number],
            required: true,
        },
    },
    data() {
        return {
            itineraryInfo: null,
            poiInfo: null,
            loading: false,
            error: null,
        };
    },
    computed: {
        klook() {
            return this.$store.state.klook || {};
        },
        language() {
            return window.KLK_LANG;
        },
        platform() {
            return 'desktop';
        },
        mapType() {
            return window.KLK_MARKET === 'global' ? 'google' : 'amap';
        },
        commonParams() {
            return {
                spu_id: this.spuId,
            };
        },
        itineraryData() {
            return {
                ...this.itineraryInfo,
                ...this.poiInfo,
            };
        },
    },
    methods: {
        getTranslateFunc() {
            return this.$t.bind(this);
        },
        getAxiosFunc() {
            // 创建一个兼容的 axios 对象，包含 $get 方法
            return {
                $get: (url, params = {}) => {
                    console.log(url, 'url', params, 'params');
                    return new Promise((resolve, reject) => {
                        try {
                            klook.ajaxGet(url, params, res => {
                                if (res && res.success) {
                                    console.log(res, 'res');
                                    resolve(res);
                                } else {
                                    reject(
                                        new Error(
                                            res?.error?.message || '请求失败',
                                        ),
                                    );
                                }
                            });
                        } catch (error) {
                            reject(error);
                        }
                    });
                },
            };
        },
        inhouseTrack(event, elem, opt = {}) {
            this.$inhouse.track(event, elem, opt);
        },
        /**
         * 获取行程信息
         * @returns {Promise<Object>} 行程数据
         */
        getItinerary() {
            return new Promise((resolve, reject) => {
                const params = {
                    ...this.commonParams,
                    translation: '',
                };
                klook.ajaxGet(
                    '/v1/agentwebserv/activity/get_itinerary',
                    params,
                    res => {
                        if (res.success) {
                            this.itineraryInfo = res.result;
                            resolve(res.result);
                        } else {
                            const errorMsg =
                                (res.error && res.error.message) ||
                                '获取行程信息失败';
                            console.error('获取行程信息失败:', errorMsg);
                            this.error = errorMsg;
                            reject(new Error(errorMsg));
                        }
                    },
                );
            });
        },
        /**
         * 获取POI信息
         * @returns {Promise<Object>} POI数据
         */
        getItineraryPoi() {
            return new Promise((resolve, reject) => {
                klook.ajaxGet(
                    '/v1/agentwebserv/activity/get_itinerary_poi',
                    this.commonParams,
                    res => {
                        if (res.success) {
                            this.poiInfo = res.result;
                            resolve(res.result);
                        } else {
                            const errorMsg =
                                (res.error && res.error.message) ||
                                '获取POI信息失败';
                            console.error('获取POI信息失败:', errorMsg);
                            this.error = errorMsg;
                            reject(new Error(errorMsg));
                        }
                    },
                );
            });
        },

        /**
         * 同时获取行程和POI信息
         * @returns {Promise<Object>} 包含行程和POI数据的对象
         */
        async fetchItineraryData() {
            this.loading = true;
            this.error = null;

            try {
                const [itineraryData, poiData] = await Promise.all([
                    this.getItinerary(),
                    this.getItineraryPoi(),
                ]);

                return {
                    itinerary: itineraryData,
                    poi: poiData,
                };
            } catch (error) {
                console.error('获取行程数据失败:', error);
                this.error = error.message || '获取行程数据失败';
                throw error;
            } finally {
                this.loading = false;
            }
        },
    },

    async mounted() {
        if (this.spuId) {
            try {
                await this.fetchItineraryData();
            } catch (error) {
                // 错误已在方法中处理，这里可以添加额外的错误处理逻辑
                console.warn('初始化行程数据失败:', error);
            }
        }
    },
};
</script>
<style lang="scss" scoped>
.itinerary {
    margin-bottom: 15px;
}
.klk-collapse-reset-style .header-box {
    color: #4c87e6;
}
</style>
