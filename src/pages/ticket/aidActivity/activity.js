import markdown from 'marked';
import { escape, offset } from '@/pages/activity/util';

const handleImages = (images, width, height) => {
    // 首先按group_id分组处理
    const groupedImages = {};
    const singleImages = [];

    images.forEach(item => {
        if (item.group_id && item.group_id.trim() !== '') {
            if (!groupedImages[item.group_id]) {
                groupedImages[item.group_id] = [];
            }
            groupedImages[item.group_id].push(item);
        } else {
            singleImages.push(item);
        }
    });

    const result = [];

    // 处理单图
    singleImages.forEach(item => {
        const curtWidth = width || item.width;
        const curtHeight = height || item.height;
        result.push({
            image_desc: item.image_desc,
            width: curtWidth,
            height: curtHeight,
            image_url: handleImageUrl(
                item.image_url || item.image_url_host,
                curtWidth,
                curtHeight,
            ),
            ifLongImage: false,
            longIamges: null,
        });
    });

    // 处理分组图片（长图）
    Object.keys(groupedImages).forEach(groupId => {
        const groupItems = groupedImages[groupId];
        if (groupItems.length > 0) {
            // 使用第一张图片的基本信息作为长图的主要信息
            const firstItem = groupItems[0];
            const curtWidth = width || firstItem.width;
            const curtHeight = height || firstItem.height;

            result.push({
                image_desc: firstItem.image_desc,
                width: curtWidth,
                height: curtHeight,
                image_url: '', // 长图不显示主图URL
                ifLongImage: true,
                longIamges: groupItems.map(img => ({
                    image_desc: img.image_desc,
                    width: img.width,
                    height: img.height,
                    image_url: handleImageUrl(
                        img.image_url || img.image_url_host,
                        img.width,
                        img.height,
                    ),
                })),
            });
        }
    });

    return result;
};

const handleImageUrl = (
    imgUrl,
    width = 1295,
    height = 720,
    title = '',
    hasWater = true,
) => {
    if (!imgUrl) {
        return '';
    }
    const imgReg = /(.*)(\.jpg|\.jpeg|\.png)$/i;
    const matches = imgReg.exec(imgUrl);
    const imageName = matches ? matches[1] : String(imgUrl).split('.')[0];
    if (!imageName) return '';
    return `${klook.getImageUrl(width, height, hasWater)}${klook.escape(
        imageName,
    )}/${escape(title || arguments[1])}`;
};

const replaceLink = (htmlStr = '') => {
    return markdown(htmlStr)
        .split('</a>')
        .map(str => {
            if (~str.indexOf('<a')) {
                return `${str.replace('<a', '<span')}</span>`;
            }
            return `${str}</a>`;
        })
        .join('');
};

const elmScrollTop = (elId, gap = 30) => {
    // 默认滚动到距离头部30px处
    const elm = document.getElementById(elId);
    if (!elm) return;
    window.scrollTo({
        top:
            offset(elm).top -
            (document.getElementById('headerContainer').clientHeight + gap),
        behavior: 'smooth',
    });
};

export { handleImages, replaceLink, elmScrollTop, handleImageUrl };
